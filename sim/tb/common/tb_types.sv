// tb_types.sv
package nmcu_tb_types;
    import nmcu_tb_params::*;

    typedef struct packed {
        nmcu_cmd_type_e cmd_type;
        logic [NMCU_ADDR_WIDTH-1:0] addr_a;
        logic [NMCU_ADDR_WIDTH-1:0] addr_b;
        logic [NMCU_ADDR_WIDTH-1:0] addr_res;
        logic [15:0] dim_m; // Matrix dimension M
        logic [15:0] dim_n; // Matrix dimension N
        logic [15:0] dim_k; // Matrix dimension K
        logic [NMCU_DATA_WIDTH-1:0] write_data; // Write data for write commands
        logic [NMCU_CMD_ID_WIDTH-1:0] cmd_id; // Command ID for tracking responses
        time start_time; // For latency measurement
    } nmcu_cmd_t;

    typedef struct packed {
        logic [NMCU_CMD_ID_WIDTH-1:0] cmd_id;
        logic [NMCU_DATA_WIDTH-1:0] read_data; // Read data for read responses
        logic success; // Whether operation was successful
        time end_time; // For latency measurement
    } nmcu_rsp_t;

    typedef enum {
        MEM_READ,
        MEM_WRITE
    } mem_op_e;

    typedef struct packed {
        mem_op_e op_type;
        logic [MEMORY_ADDR_WIDTH-1:0] addr;
        logic [MEMORY_BURST_LEN_WIDTH-1:0] burst_len; // Burst length in units of NMCU_DATA_WIDTH
        logic [MEMORY_DATA_WIDTH-1:0] wdata; // Write data
    } nmcu_mem_req_t;

    typedef struct packed {
        logic [MEMORY_ADDR_WIDTH-1:0] addr;
        logic [MEMORY_BURST_LEN_WIDTH-1:0] burst_len;
        logic [MEMORY_DATA_WIDTH-1:0] rdata; // Read data
    } nmcu_mem_rsp_t;

endpackage
