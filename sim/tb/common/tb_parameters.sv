// tb_parameters.sv
package nmcu_tb_params;
    parameter int NMCU_DATA_WIDTH = 512; // Data bus width
    parameter int NMCU_ADDR_WIDTH = 32; // Address bus width
    parameter int NMCU_CMD_ID_WIDTH = 8; // Command ID width

    // Inter-chiplet interconnect protocol related
    parameter int INTERCONNECT_DATA_WIDTH = 256;
    parameter int INTERCONNECT_CMD_TYPE_WIDTH = 4; // e.g.: 0=Read, 1=Write, 2=MatrixMult, 3=Conv
    parameter int INTERCONNECT_PACKET_OVERHEAD = 16; // Protocol header overhead in bytes

    // Memory interface related
    parameter int MEMORY_DATA_WIDTH = 512;
    parameter int MEMORY_ADDR_WIDTH = 32;
    parameter int MEMORY_BURST_LEN_WIDTH = 8; // Burst length

    // Performance metrics related
    parameter real SYS_CLK_PERIOD_NS = 0.5; // 2GHz
    parameter real NMCU_CLK_PERIOD_NS = 1.0; // 1GHz

    typedef enum {
        CMD_READ_MEM,
        CMD_WRITE_MEM,
        CMD_MATRIX_MULT,
        CMD_CONVOLUTION,
        CMD_BARRIER // Synchronization point
    } nmcu_cmd_type_e;

endpackage
