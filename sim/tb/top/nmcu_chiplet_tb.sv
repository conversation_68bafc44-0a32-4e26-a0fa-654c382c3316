// =============================================================================
// File: nmcu_chiplet_tb.sv
// Description: Top-level UVM testbench for NMCU Chiplet System
// Author: Generated for NMCU Chiplet Design Competition
// =============================================================================

`timescale 1ns/1ps

`include "uvm_macros.svh"
`include "parameters.sv"
`include "types.sv"

import uvm_pkg::*;

// Test interface includes
`include "nmcu_interfaces.sv"
`include "nmcu_sequence_items.sv"
`include "nmcu_sequences.sv"
`include "nmcu_drivers.sv"
`include "nmcu_monitors.sv"
`include "nmcu_agents.sv"
`include "nmcu_scoreboard.sv"
`include "nmcu_env.sv"
`include "nmcu_test_lib.sv"

module nmcu_chiplet_tb;

    // =========================================================================
    // Clock and Reset Generation
    // =========================================================================
    
    logic main_clk;
    logic nmcu_clk;
    logic rst_n;
    
    // Main chiplet clock - 2GHz
    initial begin
        main_clk = 0;
        forever #0.25ns main_clk = ~main_clk; // 2GHz = 0.5ns period
    end
    
    // NMCU clock - 1GHz  
    initial begin
        nmcu_clk = 0;
        forever #0.5ns nmcu_clk = ~nmcu_clk; // 1GHz = 1ns period
    end
    
    // Reset generation
    initial begin
        rst_n = 0;
        #100ns rst_n = 1;
    end

    // =========================================================================
    // Interface Instantiations
    // =========================================================================
    
    // UCIe Interconnect Interface
    ucie_interface ucie_if(main_clk, rst_n);
    
    // Memory Interface (HBM/DDR)
    memory_interface mem_if(main_clk, rst_n);
    
    // Control Interface
    control_interface ctrl_if(main_clk, rst_n);
    
    // PE Array Interface
    pe_interface pe_if(nmcu_clk, rst_n);
    
    // Cache Interface
    cache_interface cache_if(nmcu_clk, rst_n);

    // =========================================================================
    // DUT Instantiation
    // =========================================================================
    
    nmcu_chiplet #(
        .ADDR_WIDTH(`ADDR_WIDTH),
        .DATA_WIDTH(`DATA_WIDTH),
        .PE_ARRAY_SIZE(`PE_ARRAY_SIZE),
        .CACHE_SIZE(`CACHE_SIZE),
        .UCIE_LANES(`UCIE_LANES)
    ) dut (
        .main_clk(main_clk),
        .nmcu_clk(nmcu_clk),
        .rst_n(rst_n),
        
        // UCIe Interface
        .ucie_tx_clk(ucie_if.tx_clk),
        .ucie_rx_clk(ucie_if.rx_clk),
        .ucie_tx_data(ucie_if.tx_data),
        .ucie_tx_valid(ucie_if.tx_valid),
        .ucie_tx_ready(ucie_if.tx_ready),
        .ucie_rx_data(ucie_if.rx_data),
        .ucie_rx_valid(ucie_if.rx_valid),
        .ucie_rx_ready(ucie_if.rx_ready),
        
        // Memory Interface
        .mem_req_valid(mem_if.req_valid),
        .mem_req_ready(mem_if.req_ready),
        .mem_req_addr(mem_if.req_addr),
        .mem_req_data(mem_if.req_data),
        .mem_req_cmd(mem_if.req_cmd),
        .mem_resp_valid(mem_if.resp_valid),
        .mem_resp_ready(mem_if.resp_ready),
        .mem_resp_data(mem_if.resp_data),
        .mem_resp_tag(mem_if.resp_tag),
        
        // Control Interface
        .ctrl_cmd_valid(ctrl_if.cmd_valid),
        .ctrl_cmd_ready(ctrl_if.cmd_ready),
        .ctrl_cmd_opcode(ctrl_if.cmd_opcode),
        .ctrl_cmd_addr(ctrl_if.cmd_addr),
        .ctrl_cmd_data(ctrl_if.cmd_data),
        .ctrl_resp_valid(ctrl_if.resp_valid),
        .ctrl_resp_ready(ctrl_if.resp_ready),
        .ctrl_resp_data(ctrl_if.resp_data),
        .ctrl_resp_status(ctrl_if.resp_status)
    );

    // =========================================================================
    // Memory Models Instantiation
    // =========================================================================
    
    // HBM Memory Model
    hbm_model #(
        .ADDR_WIDTH(`ADDR_WIDTH),
        .DATA_WIDTH(`DATA_WIDTH),
        .BURST_LENGTH(8)
    ) hbm_model_inst (
        .clk(main_clk),
        .rst_n(rst_n),
        .req_valid(mem_if.req_valid),
        .req_ready(mem_if.req_ready),
        .req_addr(mem_if.req_addr),
        .req_data(mem_if.req_data),
        .req_cmd(mem_if.req_cmd),
        .resp_valid(mem_if.resp_valid),
        .resp_ready(mem_if.resp_ready),
        .resp_data(mem_if.resp_data),
        .resp_tag(mem_if.resp_tag)
    );
    
    // UCIe IP Model (Loopback for inter-chiplet communication)
    ucie_ip_model #(
        .LANES(`UCIE_LANES),
        .DATA_WIDTH(`UCIE_DATA_WIDTH)
    ) ucie_model_inst (
        .tx_clk(ucie_if.tx_clk),
        .rx_clk(ucie_if.rx_clk),
        .rst_n(rst_n),
        .tx_data(ucie_if.tx_data),
        .tx_valid(ucie_if.tx_valid),
        .tx_ready(ucie_if.tx_ready),
        .rx_data(ucie_if.rx_data),
        .rx_valid(ucie_if.rx_valid),
        .rx_ready(ucie_if.rx_ready)
    );

    // =========================================================================
    // UVM Test Environment Setup
    // =========================================================================
    
    initial begin
        // Set interfaces in UVM config database
        uvm_config_db#(virtual ucie_interface)::set(null, "*", "ucie_vif", ucie_if);
        uvm_config_db#(virtual memory_interface)::set(null, "*", "mem_vif", mem_if);
        uvm_config_db#(virtual control_interface)::set(null, "*", "ctrl_vif", ctrl_if);
        uvm_config_db#(virtual pe_interface)::set(null, "*", "pe_vif", pe_if);
        uvm_config_db#(virtual cache_interface)::set(null, "*", "cache_vif", cache_if);
        
        // Set test configuration
        uvm_config_db#(int)::set(null, "*", "main_clk_freq", 2000); // MHz
        uvm_config_db#(int)::set(null, "*", "nmcu_clk_freq", 1000); // MHz
        uvm_config_db#(int)::set(null, "*", "test_timeout", 100000); // ns
        
        // Enable UVM logging
        uvm_config_db#(int)::set(null, "*", "recording_detail", UVM_FULL);
        
        // Run the test
        run_test();
    end

    // =========================================================================
    // Waveform Dumping
    // =========================================================================
    
    initial begin
        if ($test$plusargs("DUMP_VCD")) begin
            $dumpfile("nmcu_chiplet.vcd");
            $dumpvars(0, nmcu_chiplet_tb);
        end
        
        if ($test$plusargs("DUMP_FSDB")) begin
            $fsdbDumpfile("nmcu_chiplet.fsdb");
            $fsdbDumpvars(0, nmcu_chiplet_tb);
        end
    end

    // =========================================================================
    // Simulation Control and Watchdog
    // =========================================================================
    
    initial begin
        #10ms; // Maximum simulation time
        `uvm_error("TB_TIMEOUT", "Simulation timeout reached!")
        $finish;
    end
    
    // Clock monitoring
    initial begin
        wait(rst_n);
        fork
            begin
                forever begin
                    @(posedge main_clk);
                    if ($time > 1000ns) begin // After initial settling
                        assert(main_clk !== 1'bx) else 
                            `uvm_error("CLK_CHECK", "Main clock went to X state")
                    end
                end
            end
            begin
                forever begin
                    @(posedge nmcu_clk);
                    if ($time > 1000ns) begin
                        assert(nmcu_clk !== 1'bx) else 
                            `uvm_error("CLK_CHECK", "NMCU clock went to X state")
                    end
                end
            end
        join_none
    end

    // =========================================================================
    // Coverage Collection
    // =========================================================================
    
    // System-level functional coverage
    covergroup system_coverage @(posedge main_clk);
        option.per_instance = 1;
        option.name = "nmcu_system_cov";
        
        cp_reset: coverpoint rst_n {
            bins reset_deasserted = {1};
            bins reset_asserted = {0};
        }
        
        cp_ucie_activity: coverpoint ucie_if.tx_valid {
            bins idle = {0};
            bins active = {1};
        }
        
        cp_mem_activity: coverpoint mem_if.req_valid {
            bins idle = {0};
            bins active = {1};
        }
        
        cp_ctrl_activity: coverpoint ctrl_if.cmd_valid {
            bins idle = {0};
            bins active = {1};
        }
        
        // Cross coverage for concurrent activities
        cx_concurrent_ops: cross cp_ucie_activity, cp_mem_activity, cp_ctrl_activity;
    endgroup
    
    system_coverage sys_cov = new();

    // =========================================================================
    // Performance Monitoring
    // =========================================================================
    
    // Performance counters
    int unsigned cycle_count = 0;
    int unsigned mem_requests = 0;
    int unsigned ucie_transfers = 0;
    int unsigned pe_operations = 0;
    
    always @(posedge main_clk) begin
        if (rst_n) begin
            cycle_count++;
            
            if (mem_if.req_valid && mem_if.req_ready)
                mem_requests++;
                
            if (ucie_if.tx_valid && ucie_if.tx_ready)
                ucie_transfers++;
        end
    end
    
    always @(posedge nmcu_clk) begin
        if (rst_n) begin
            // Monitor PE array activity (simplified)
            if (pe_if.pe_start && pe_if.pe_ready)
                pe_operations++;
        end
    end
    
    // Performance reporting
    final begin
        real mem_bandwidth, ucie_bandwidth;
        real simulation_time_ns = $time;
        
        mem_bandwidth = (mem_requests * `DATA_WIDTH * 1000.0) / simulation_time_ns; // Gbps
        ucie_bandwidth = (ucie_transfers * `UCIE_DATA_WIDTH * 1000.0) / simulation_time_ns; // Gbps
        
        `uvm_info("PERF_REPORT", $sformatf(
            "Performance Summary:\n" +
            "  Simulation Time: %.2f us\n" +
            "  Total Cycles (2GHz): %0d\n" +
            "  Memory Requests: %0d\n" +
            "  UCIe Transfers: %0d\n" +
            "  PE Operations: %0d\n" +
            "  Memory Bandwidth: %.2f Gbps\n" +
            "  UCIe Bandwidth: %.2f Gbps",
            simulation_time_ns/1000.0,
            cycle_count,
            mem_requests,
            ucie_transfers,
            pe_operations,
            mem_bandwidth,
            ucie_bandwidth
        ), UVM_LOW)
    end

    // =========================================================================
    // Protocol Checkers and Assertions
    // =========================================================================
    
    // UCIe Protocol Checker
    property ucie_valid_ready_handshake;
        @(posedge main_clk) disable iff (!rst_n)
        ucie_if.tx_valid && !ucie_if.tx_ready |=> ucie_if.tx_valid;
    endproperty
    
    assert property (ucie_valid_ready_handshake) else
        `uvm_error("UCIE_PROTOCOL", "UCIe TX valid/ready handshake violation")
    
    // Memory Interface Protocol Checker
    property memory_req_stable;
        @(posedge main_clk) disable iff (!rst_n)
        mem_if.req_valid && !mem_if.req_ready |=> 
        $stable(mem_if.req_addr) && $stable(mem_if.req_cmd);
    endproperty
    
    assert property (memory_req_stable) else
        `uvm_error("MEM_PROTOCOL", "Memory request signals not stable during wait")
    
    // Cache coherency checker (simplified)
    property cache_response_order;
        @(posedge nmcu_clk) disable iff (!rst_n)
        cache_if.req_valid && cache_if.req_ready |-> 
        ##[1:50] cache_if.resp_valid;
    endproperty
    
    assert property (cache_response_order) else
        `uvm_warning("CACHE_TIMING", "Cache response took longer than expected")

    // =========================================================================
    // Debug and Analysis
    // =========================================================================
    
    `ifdef DEBUG_MODE
    // Transaction logging for debug
    always @(posedge main_clk) begin
        if (rst_n && mem_if.req_valid && mem_if.req_ready) begin
            `uvm_info("DEBUG", $sformatf(
                "Memory Request: addr=0x%h, cmd=%s, data=0x%h", 
                mem_if.req_addr, 
                mem_if.req_cmd.name(),
                mem_if.req_data
            ), UVM_DEBUG)
        end
        
        if (rst_n && ucie_if.tx_valid && ucie_if.tx_ready) begin
            `uvm_info("DEBUG", $sformatf(
                "UCIe Transfer: data=0x%h", 
                ucie_if.tx_data
            ), UVM_DEBUG)
        end
    end
    `endif

endmodule

// =============================================================================
// Additional Test Modules for Specific Component Testing
// =============================================================================

// Prefetcher-specific testbench
module prefetcher_tb;
    
    logic clk, rst_n;
    
    // Clock generation
    initial begin
        clk = 0;
        forever #0.5ns clk = ~clk; // 1GHz
    end
    
    // Reset generation
    initial begin
        rst_n = 0;
        #10ns rst_n = 1;
    end
    
    // Prefetcher interface signals
    logic miss_valid, miss_ready;
    logic [`ADDR_WIDTH-1:0] miss_addr;
    logic hit_valid;
    logic [`ADDR_WIDTH-1:0] hit_addr;
    logic prefetch_req_valid, prefetch_req_ready;
    logic [`ADDR_WIDTH-1:0] prefetch_req_addr;
    logic prefetch_resp_valid, prefetch_resp_ready;
    logic [`ADDR_WIDTH-1:0] prefetch_resp_addr;
    logic [`DATA_WIDTH-1:0] prefetch_resp_data;
    logic prefetch_enable;
    logic [2:0] prefetch_aggressiveness;
    logic [31:0] prefetch_hits, prefetch_requests, prefetch_useful;
    
    // Prefetcher DUT
    prefetcher #(
        .ADDR_WIDTH(`ADDR_WIDTH),
        .DATA_WIDTH(`DATA_WIDTH),
        .CACHE_LINE_SIZE(`CACHE_LINE_SIZE)
    ) prefetcher_dut (
        .clk(clk),
        .rst_n(rst_n),
        .miss_valid(miss_valid),
        .miss_addr(miss_addr),
        .miss_ready(miss_ready),
        .hit_valid(hit_valid),
        .hit_addr(hit_addr),
        .prefetch_req_valid(prefetch_req_valid),
        .prefetch_req_addr(prefetch_req_addr),
        .prefetch_req_ready(prefetch_req_ready),
        .prefetch_resp_valid(prefetch_resp_valid),
        .prefetch_resp_addr(prefetch_resp_addr),
        .prefetch_resp_data(prefetch_resp_data),
        .prefetch_resp_ready(prefetch_resp_ready),
        .prefetch_enable(prefetch_enable),
        .prefetch_aggressiveness(prefetch_aggressiveness),
        .prefetch_hits(prefetch_hits),
        .prefetch_requests(prefetch_requests),
        .prefetch_useful(prefetch_useful)
    );
    
    // Simple test stimulus
    initial begin
        // Initialize
        miss_valid = 0;
        miss_addr = 0;
        miss_ready = 1;
        hit_valid = 0;
        hit_addr = 0;
        prefetch_req_ready = 1;
        prefetch_resp_valid = 0;
        prefetch_resp_addr = 0;
        prefetch_resp_data = 0;
        prefetch_enable = 1;
        prefetch_aggressiveness = 3'h4;
        
        wait(rst_n);
        repeat(10) @(posedge clk);
        
        // Test stride pattern
        for (int i = 0; i < 10; i++) begin
            miss_valid = 1;
            miss_addr = 64 * i; // Stride of 64 bytes
            @(posedge clk);
            miss_valid = 0;
            repeat(5) @(posedge clk);
        end
        
        repeat(100) @(posedge clk);
        
        `uvm_info("PREFETCH_TEST", $sformatf(
            "Prefetcher Stats: Requests=%0d, Hits=%0d, Useful=%0d",
            prefetch_requests, prefetch_hits, prefetch_useful
        ), UVM_LOW)
        
        $finish;
    end
    
    // Monitor prefetch requests
    always @(posedge clk) begin
        if (prefetch_req_valid && prefetch_req_ready) begin
            `uvm_info("PREFETCH_REQ", $sformatf(
                "Prefetch request: addr=0x%h", prefetch_req_addr
            ), UVM_MEDIUM)
        end
    end
    
endmodule