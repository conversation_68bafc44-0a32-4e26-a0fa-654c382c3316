// nmcu_interconnect_if.sv
// Interface for NMCU (Network Management Control Unit) interconnect communication
// Provides bidirectional communication between testbench and DUT with handshaking
interface nmcu_interconnect_if (input logic clk, input logic rst_n);
    import nmcu_tb_params::*;  // Import testbench parameters package
    import nmcu_tb_types::*;   // Import testbench type definitions package

    // Command Channel (TB -> DUT)
    // This channel allows the testbench to send commands to the DUT
    logic                         cmd_valid;    // Indicates command is valid and ready to be sent
    logic                         cmd_ready;    // Backpressure signal from DUT (ready to accept command)
    nmcu_cmd_t                    cmd_payload;  // The actual command data being transmitted

    // Response Channel (DUT -> TB)
    // This channel allows the DUT to send responses back to the testbench
    logic                         rsp_valid;    // Indicates response is valid and ready to be sent
    logic                         rsp_ready;    // Backpressure signal from TB (ready to accept response)
    nmcu_rsp_t                    rsp_payload;  // The actual response data being transmitted

    // Modports for different connections
    // TB modport: Defines signals as seen from the testbench perspective
    modport TB (
        output cmd_valid,     // TB drives command valid signal
        input  cmd_ready,     // TB receives ready signal from DUT
        output cmd_payload,   // TB drives command data
        input  rsp_valid,     // TB receives response valid from DUT
        output rsp_ready,     // TB drives response ready signal
        input  rsp_payload,   // TB receives response data from DUT
        input  clk,           // Clock input
        input  rst_n          // Active-low reset input
    );

    // DUT modport: Defines signals as seen from the DUT perspective
    modport DUT (
        input  cmd_valid,     // DUT receives command valid signal
        output cmd_ready,     // DUT drives ready signal to TB
        input  cmd_payload,   // DUT receives command data
        output rsp_valid,     // DUT drives response valid signal
        input  rsp_ready,     // DUT receives response ready from TB
        output rsp_payload,   // DUT drives response data to TB
        input  clk,           // Clock input
        input  rst_n          // Active-low reset input
    );

    // Assertion for basic handshaking protocol validation
    // Currently commented out - can be enabled for debugging handshake issues
    // property cmd_handshake;
    //   @(posedge clk) (cmd_valid && !cmd_ready) |=> !cmd_ready; // Valid stays asserted until ready
    // endproperty
    // CMD_HANDSHAKE_ASSERT: assert property (cmd_handshake) else $error("Interconnect command handshake violation");

endinterface
