// control_interface.sv
// Control Interface Definition for NMCU Control Unit
// Simplified interface for Verilator compatibility

`include "parameters.sv"
`include "types.sv"

interface control_interface (
    input logic clk,
    input logic rst_n
);
    import nmcu_pkg::*;
    import nmcu_types::*;

    // Command Channel
    logic                           cmd_valid;
    logic                           cmd_ready;
    logic [CMD_OPCODE_WIDTH-1:0]    cmd_opcode;
    logic [ADDR_WIDTH-1:0]          cmd_addr;
    logic [DATA_WIDTH-1:0]          cmd_data;
    logic [15:0]                    cmd_length;
    logic [15:0]                    cmd_config;
    logic [3:0]                     cmd_id;
    
    // Response Channel
    logic                           resp_valid;
    logic                           resp_ready;
    logic [DATA_WIDTH-1:0]          resp_data;
    logic [STATUS_WIDTH-1:0]        resp_status;
    logic [3:0]                     resp_id;
    logic                           resp_error;
    
    // Status and Control Signals
    logic                           ctrl_enable;
    logic                           ctrl_reset;
    logic                           ctrl_idle;
    logic                           ctrl_busy;
    logic [31:0]                    status_reg;
    logic [7:0]                     error_code;
    
    // Interrupt and Event Signals
    logic                           interrupt;
    logic                           event_complete;
    logic                           event_error;
    logic [7:0]                     event_mask;
    
    // Modport for master (testbench side)
    modport master (
        input  clk,
        input  rst_n,
        output cmd_valid,
        input  cmd_ready,
        output cmd_opcode,
        output cmd_addr,
        output cmd_data,
        output cmd_length,
        output cmd_config,
        output cmd_id,
        input  resp_valid,
        output resp_ready,
        input  resp_data,
        input  resp_status,
        input  resp_id,
        input  resp_error,
        output ctrl_enable,
        output ctrl_reset,
        input  ctrl_idle,
        input  ctrl_busy,
        input  status_reg,
        input  error_code,
        input  interrupt,
        input  event_complete,
        input  event_error,
        output event_mask
    );
    
    // Modport for slave (DUT control unit side)
    modport slave (
        input  clk,
        input  rst_n,
        input  cmd_valid,
        output cmd_ready,
        input  cmd_opcode,
        input  cmd_addr,
        input  cmd_data,
        input  cmd_length,
        input  cmd_config,
        input  cmd_id,
        output resp_valid,
        input  resp_ready,
        output resp_data,
        output resp_status,
        output resp_id,
        output resp_error,
        input  ctrl_enable,
        input  ctrl_reset,
        output ctrl_idle,
        output ctrl_busy,
        output status_reg,
        output error_code,
        output interrupt,
        output event_complete,
        output event_error,
        input  event_mask
    );

    // Basic protocol assertions (can be disabled for Verilator)
    `ifdef ENABLE_ASSERTIONS
        // Command handshake protocol
        property cmd_handshake;
            @(posedge clk) disable iff (!rst_n)
            cmd_valid && !cmd_ready |=> cmd_valid;
        endproperty
        
        assert property (cmd_handshake) else 
            $error("Control command handshake violation");
        
        // Response handshake protocol
        property resp_handshake;
            @(posedge clk) disable iff (!rst_n)
            resp_valid && !resp_ready |=> resp_valid;
        endproperty
        
        assert property (resp_handshake) else 
            $error("Control response handshake violation");
            
        // Mutual exclusion of idle and busy
        property idle_busy_mutex;
            @(posedge clk) disable iff (!rst_n)
            !(ctrl_idle && ctrl_busy);
        endproperty
        
        assert property (idle_busy_mutex) else 
            $error("Control unit cannot be both idle and busy");
    `endif

endinterface
