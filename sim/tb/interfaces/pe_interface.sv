// pe_interface.sv
// Processing Element (PE) Array Interface Definition
// Simplified interface for Verilator compatibility

`include "parameters.sv"
`include "types.sv"

interface pe_if (
    input logic clk,
    input logic rst_n
);
    import nmcu_pkg::*;
    import nmcu_types::*;

    // PE Control Signals
    logic                           pe_enable;
    logic                           pe_start;
    logic                           pe_ready;
    logic                           pe_done;
    logic                           pe_idle;
    logic                           pe_busy;
    
    // PE Configuration
    logic [7:0]                     pe_config;
    logic [PE_ARRAY_SIZE_X-1:0]     pe_x_enable;
    logic [PE_ARRAY_SIZE_Y-1:0]     pe_y_enable;
    logic [2:0]                     pe_operation;   // pe_op_e
    
    // Input Data Channel
    logic                           input_valid;
    logic                           input_ready;
    logic [DATA_WIDTH-1:0]          input_data;
    logic [$clog2(PE_ARRAY_SIZE_X)-1:0] input_x_addr;
    logic [$clog2(PE_ARRAY_SIZE_Y)-1:0] input_y_addr;
    logic                           input_broadcast;
    
    // Weight Data Channel
    logic                           weight_valid;
    logic                           weight_ready;
    logic [WEIGHT_WIDTH-1:0]        weight_data;
    logic [$clog2(PE_ARRAY_SIZE_X)-1:0] weight_x_addr;
    logic [$clog2(PE_ARRAY_SIZE_Y)-1:0] weight_y_addr;
    logic                           weight_load_en;
    
    // Result Data Channel
    logic                           result_valid;
    logic                           result_ready;
    logic [RESULT_WIDTH-1:0]        result_data;
    logic [$clog2(PE_ARRAY_SIZE_X)-1:0] result_x_addr;
    logic [$clog2(PE_ARRAY_SIZE_Y)-1:0] result_y_addr;
    logic                           result_accumulate;
    
    // Partial Sum Channel (for systolic operation)
    logic                           partial_sum_valid;
    logic [RESULT_WIDTH-1:0]        partial_sum_data;
    logic                           partial_sum_clear;
    
    // Status and Debug
    logic [PE_ARRAY_SIZE_X-1:0]     pe_x_status;
    logic [PE_ARRAY_SIZE_Y-1:0]     pe_y_status;
    logic [7:0]                     pe_error_status;
    logic [15:0]                    pe_cycle_count;
    
    // Modport for master (control unit side)
    modport master (
        input  clk,
        input  rst_n,
        output pe_enable,
        output pe_start,
        input  pe_ready,
        input  pe_done,
        input  pe_idle,
        input  pe_busy,
        output pe_config,
        output pe_x_enable,
        output pe_y_enable,
        output pe_operation,
        output input_valid,
        input  input_ready,
        output input_data,
        output input_x_addr,
        output input_y_addr,
        output input_broadcast,
        output weight_valid,
        input  weight_ready,
        output weight_data,
        output weight_x_addr,
        output weight_y_addr,
        output weight_load_en,
        input  result_valid,
        output result_ready,
        input  result_data,
        input  result_x_addr,
        input  result_y_addr,
        input  result_accumulate,
        input  partial_sum_valid,
        input  partial_sum_data,
        output partial_sum_clear,
        input  pe_x_status,
        input  pe_y_status,
        input  pe_error_status,
        input  pe_cycle_count
    );
    
    // Modport for slave (PE array side)
    modport slave (
        input  clk,
        input  rst_n,
        input  pe_enable,
        input  pe_start,
        output pe_ready,
        output pe_done,
        output pe_idle,
        output pe_busy,
        input  pe_config,
        input  pe_x_enable,
        input  pe_y_enable,
        input  pe_operation,
        input  input_valid,
        output input_ready,
        input  input_data,
        input  input_x_addr,
        input  input_y_addr,
        input  input_broadcast,
        input  weight_valid,
        output weight_ready,
        input  weight_data,
        input  weight_x_addr,
        input  weight_y_addr,
        input  weight_load_en,
        output result_valid,
        input  result_ready,
        output result_data,
        output result_x_addr,
        output result_y_addr,
        output result_accumulate,
        output partial_sum_valid,
        output partial_sum_data,
        input  partial_sum_clear,
        output pe_x_status,
        output pe_y_status,
        output pe_error_status,
        output pe_cycle_count
    );

    // Basic protocol assertions (can be disabled for Verilator)
    `ifdef ENABLE_ASSERTIONS
        // Input handshake protocol
        property input_handshake;
            @(posedge clk) disable iff (!rst_n)
            input_valid && !input_ready |=> input_valid;
        endproperty
        
        assert property (input_handshake) else 
            $error("PE input handshake violation");
        
        // Weight handshake protocol
        property weight_handshake;
            @(posedge clk) disable iff (!rst_n)
            weight_valid && !weight_ready |=> weight_valid;
        endproperty
        
        assert property (weight_handshake) else 
            $error("PE weight handshake violation");
            
        // Result handshake protocol
        property result_handshake;
            @(posedge clk) disable iff (!rst_n)
            result_valid && !result_ready |=> result_valid;
        endproperty
        
        assert property (result_handshake) else 
            $error("PE result handshake violation");
    `endif

endinterface
