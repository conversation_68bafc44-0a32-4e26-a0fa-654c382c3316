// cache_interface.sv
// Cache Interface Definition for NMCU Cache System
// Simplified interface for Verilator compatibility

`include "parameters.sv"
`include "types.sv"

interface cache_interface (
    input logic clk,
    input logic rst_n
);
    import nmcu_pkg::*;
    import nmcu_types::*;

    // Cache Request Channel
    logic                           req_valid;
    logic                           req_ready;
    logic [2:0]                     req_type;       // cache_req_type_e
    logic [ADDR_WIDTH-1:0]          req_addr;
    logic [CACHE_LINE_WIDTH-1:0]    req_data;
    logic [3:0]                     req_id;
    logic [CACHE_LINE_SIZE-1:0]     req_be;         // Byte enable
    
    // Cache Response Channel
    logic                           resp_valid;
    logic                           resp_ready;
    logic [CACHE_LINE_WIDTH-1:0]    resp_data;
    logic                           resp_hit;
    logic                           resp_miss;
    logic [3:0]                     resp_id;
    logic                           resp_error;
    
    // Cache Control and Status
    logic                           cache_enable;
    logic                           cache_flush;
    logic                           cache_invalidate;
    logic                           cache_flush_done;
    logic                           cache_inv_done;
    
    // Cache Statistics and Debug
    logic [31:0]                    cache_hits;
    logic [31:0]                    cache_misses;
    logic [31:0]                    cache_evictions;
    logic [7:0]                     cache_occupancy;
    logic [CACHE_WAYS-1:0]          cache_way_valid;
    logic [CACHE_WAYS-1:0]          cache_way_dirty;
    
    // Cache Line Status
    logic [CACHE_TAG_WIDTH-1:0]     cache_tag;
    logic [CACHE_INDEX_WIDTH-1:0]   cache_index;
    logic [CACHE_OFFSET_WIDTH-1:0]  cache_offset;
    logic [1:0]                     cache_state;    // cache_state_e
    
    // Prefetch Interface
    logic                           prefetch_req;
    logic                           prefetch_ready;
    logic [ADDR_WIDTH-1:0]          prefetch_addr;
    logic [1:0]                     prefetch_type;  // prefetch_type_e
    
    // Modport for master (control unit side)
    modport master (
        input  clk,
        input  rst_n,
        output req_valid,
        input  req_ready,
        output req_type,
        output req_addr,
        output req_data,
        output req_id,
        output req_be,
        input  resp_valid,
        output resp_ready,
        input  resp_data,
        input  resp_hit,
        input  resp_miss,
        input  resp_id,
        input  resp_error,
        output cache_enable,
        output cache_flush,
        output cache_invalidate,
        input  cache_flush_done,
        input  cache_inv_done,
        input  cache_hits,
        input  cache_misses,
        input  cache_evictions,
        input  cache_occupancy,
        input  cache_way_valid,
        input  cache_way_dirty,
        input  cache_tag,
        input  cache_index,
        input  cache_offset,
        input  cache_state,
        output prefetch_req,
        input  prefetch_ready,
        output prefetch_addr,
        output prefetch_type
    );

    // Modport for slave (cache memory side)
    modport slave (
        input  clk,
        input  rst_n,
        input  req_valid,
        output req_ready,
        input  req_type,
        input  req_addr,
        input  req_data,
        input  req_id,
        input  req_be,
        output resp_valid,
        input  resp_ready,
        output resp_data,
        output resp_hit,
        output resp_miss,
        output resp_id,
        output resp_error,
        input  cache_enable,
        input  cache_flush,
        input  cache_invalidate,
        output cache_flush_done,
        output cache_inv_done,
        output cache_hits,
        output cache_misses,
        output cache_evictions,
        output cache_occupancy,
        output cache_way_valid,
        output cache_way_dirty,
        output cache_tag,
        output cache_index,
        output cache_offset,
        output cache_state,
        input  prefetch_req,
        output prefetch_ready,
        input  prefetch_addr,
        input  prefetch_type
    );

    // Address decomposition
    assign cache_tag = req_addr[ADDR_WIDTH-1:CACHE_INDEX_WIDTH+CACHE_OFFSET_WIDTH];
    assign cache_index = req_addr[CACHE_INDEX_WIDTH+CACHE_OFFSET_WIDTH-1:CACHE_OFFSET_WIDTH];
    assign cache_offset = req_addr[CACHE_OFFSET_WIDTH-1:0];

    // Basic protocol assertions (can be disabled for Verilator)
    `ifdef ENABLE_ASSERTIONS
        // Request handshake protocol
        property req_handshake;
            @(posedge clk) disable iff (!rst_n)
            req_valid && !req_ready |=> req_valid;
        endproperty

        assert property (req_handshake) else 
            $error("Cache request handshake violation");

        // Response handshake protocol
        property resp_handshake;
            @(posedge clk) disable iff (!rst_n)
            resp_valid && !resp_ready |=> resp_valid;
        endproperty

        assert property (resp_handshake) else 
            $error("Cache response handshake violation");

        // Hit/miss mutual exclusion
        property hit_miss_mutex;
            @(posedge clk) disable iff (!rst_n)
            resp_valid |-> !(resp_hit && resp_miss);
        endproperty
        
        assert property (hit_miss_mutex) else 
            $error("Cache cannot have both hit and miss");
    `endif

endinterface
