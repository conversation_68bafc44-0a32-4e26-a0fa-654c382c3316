// mem_interface.sv
// Memory Interface Definition - alias for memory_interface
// For compatibility with existing DUT

`include "parameters.sv"
`include "types.sv"

interface mem_interface (
    input logic clk,
    input logic rst_n
);
    import nmcu_pkg::*;
    import nmcu_types::*;

    // Memory Request Channel
    logic                           req_valid;
    logic                           req_ready;
    logic [ADDR_WIDTH-1:0]          req_addr;
    logic [MEM_DATA_WIDTH-1:0]      req_data;
    logic [2:0]                     req_cmd;        // mem_cmd_e
    logic [7:0]                     req_burst_len;
    logic [3:0]                     req_id;
    logic [MEM_DATA_WIDTH/8-1:0]    req_be;         // Byte enable
    
    // Memory Response Channel
    logic                           resp_valid;
    logic                           resp_ready;
    logic [MEM_DATA_WIDTH-1:0]      resp_data;
    logic [3:0]                     resp_id;
    logic                           resp_error;
    logic [1:0]                     resp_status;
    logic [7:0]                     resp_tag;
    
    // Memory Control and Status
    logic                           mem_clk;
    logic                           mem_rst_n;
    logic                           mem_init_done;
    logic                           mem_cal_done;
    logic [7:0]                     mem_status;
    
    // Modport for master (DUT side - memory controller)
    modport master (
        input  clk,
        input  rst_n,
        output req_valid,
        input  req_ready,
        output req_addr,
        output req_data,
        output req_cmd,
        output req_burst_len,
        output req_id,
        output req_be,
        input  resp_valid,
        output resp_ready,
        input  resp_data,
        input  resp_id,
        input  resp_error,
        input  resp_status,
        input  resp_tag,
        output mem_clk,
        output mem_rst_n,
        input  mem_init_done,
        input  mem_cal_done,
        input  mem_status
    );
    
    // Modport for slave (memory model side)
    modport slave (
        input  clk,
        input  rst_n,
        input  req_valid,
        output req_ready,
        input  req_addr,
        input  req_data,
        input  req_cmd,
        input  req_burst_len,
        input  req_id,
        input  req_be,
        output resp_valid,
        input  resp_ready,
        output resp_data,
        output resp_id,
        output resp_error,
        output resp_status,
        output resp_tag,
        input  mem_clk,
        input  mem_rst_n,
        output mem_init_done,
        output mem_cal_done,
        output mem_status
    );

    // Clock assignment
    assign mem_clk = clk;
    assign mem_rst_n = rst_n;

endinterface
