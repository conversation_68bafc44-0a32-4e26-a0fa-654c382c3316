// ucie_interface.sv
// UCIe (Universal Chiplet Interconnect Express) Interface Definition
// Simplified interface for Verilator compatibility

`include "parameters.sv"
`include "types.sv"

interface ucie_interface (
    input logic clk,
    input logic rst_n
);
    import nmcu_pkg::*;
    import nmcu_types::*;

    // UCIe TX Channel (Transmit)
    logic                           tx_clk;
    logic                           tx_valid;
    logic                           tx_ready;
    logic [UCIE_FLIT_WIDTH-1:0]     tx_data;
    logic [UCIE_LANES-1:0]          tx_lanes;
    
    // UCIe RX Channel (Receive)
    logic                           rx_clk;
    logic                           rx_valid;
    logic                           rx_ready;
    logic [UCIE_FLIT_WIDTH-1:0]     rx_data;
    logic [UCIE_LANES-1:0]          rx_lanes;
    
    // Control and Status
    logic                           link_up;
    logic                           link_training;
    logic [7:0]                     link_status;
    logic [3:0]                     error_status;
    
    // Flow Control
    logic [7:0]                     tx_credits;
    logic [7:0]                     rx_credits;
    logic                           credit_update;
    
    // Modport for slave (DUT side)
    modport slave (
        input  clk,
        input  rst_n,
        output tx_clk,
        output tx_valid,
        input  tx_ready,
        output tx_data,
        output tx_lanes,
        input  rx_clk,
        input  rx_valid,
        output rx_ready,
        input  rx_data,
        input  rx_lanes,
        input  link_up,
        input  link_training,
        input  link_status,
        output error_status,
        input  tx_credits,
        output rx_credits,
        input  credit_update
    );
    
    // Modport for master (testbench/model side)
    modport master (
        input  clk,
        input  rst_n,
        input  tx_clk,
        input  tx_valid,
        output tx_ready,
        input  tx_data,
        input  tx_lanes,
        output rx_clk,
        output rx_valid,
        input  rx_ready,
        output rx_data,
        output rx_lanes,
        output link_up,
        output link_training,
        output link_status,
        input  error_status,
        output tx_credits,
        input  rx_credits,
        output credit_update
    );

    // Clock generation for UCIe (simplified)
    assign tx_clk = clk;
    assign rx_clk = clk;
    
    // Basic protocol assertions (can be disabled for Verilator)
    `ifdef ENABLE_ASSERTIONS
        // TX handshake protocol
        property tx_handshake;
            @(posedge tx_clk) disable iff (!rst_n)
            tx_valid && !tx_ready |=> tx_valid;
        endproperty
        
        assert property (tx_handshake) else 
            $error("UCIe TX handshake violation");
        
        // RX handshake protocol
        property rx_handshake;
            @(posedge rx_clk) disable iff (!rst_n)
            rx_valid && !rx_ready |=> rx_valid;
        endproperty
        
        assert property (rx_handshake) else 
            $error("UCIe RX handshake violation");
    `endif

endinterface
