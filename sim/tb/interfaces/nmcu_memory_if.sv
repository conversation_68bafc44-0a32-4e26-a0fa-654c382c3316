// nmcu_memory_if.sv
// Interface for connecting DUT memory controller to memory model (HBM/DDR)
interface nmcu_memory_if (input logic clk, input logic rst_n);
    import nmcu_tb_params::*;
    import nmcu_tb_types::*;

    // Request Channel (DUT -> MEM_MODEL)
    // Valid signal indicates when request data is available
    logic                      req_valid;
    // Ready signal provides backpressure from memory model
    logic                      req_ready; 
    // Request payload containing address, data, and control signals
    nmcu_mem_req_t             req_payload;

    // Response Channel (MEM_MODEL -> DUT)
    // Valid signal indicates when response data is available
    logic                      rsp_valid;
    // Ready signal provides backpressure from DUT
    logic                      rsp_ready; 
    // Response payload containing read data and status
    nmcu_mem_rsp_t             rsp_payload;

    // Modport for testbench memory model (HBM/DDR simulator)
    modport TB_MODEL ( 
        input  req_valid,    // Receive request valid from DUT
        output req_ready,    // Send ready back to DUT
        input  req_payload,  // Receive request data from DUT
        output rsp_valid,    // Send response valid to DUT
        input  rsp_ready,    // Receive ready from DUT
        output rsp_payload,  // Send response data to DUT
        input  clk,          // Clock input
        input  rst_n         // Active-low reset
    );

    // Modport for design under test (memory interface controller)
    modport DUT ( 
        output req_valid,    // Send request valid to memory model
        input  req_ready,    // Receive ready from memory model
        output req_payload,  // Send request data to memory model
        input  rsp_valid,    // Receive response valid from memory model
        output rsp_ready,    // Send ready to memory model
        input  rsp_payload,  // Receive response data from memory model
        input  clk,          // Clock input
        input  rst_n         // Active-low reset
    );

endinterface
