// =============================================================================
// File: hbm_model.sv
// Description: HBM (High Bandwidth Memory) Behavioral Model for NMCU Simulation
// =============================================================================

import nmcu_pkg::*;
import nmcu_types::*;

module hbm_model #(
    parameter int ADDR_WIDTH = 34,        // HBM地址宽度
    parameter int DATA_WIDTH = 1024,      // HBM数据宽度 (128字节/传输)
    parameter int BURST_LENGTH = 4,       // 突发长度
    parameter int NUM_CHANNELS = 8,       // HBM通道数
    parameter int NUM_RANKS = 2,          // 每通道rank数
    parameter int NUM_BANKS = 16,         // 每rank bank数
    parameter int MEMORY_SIZE = 32'h4000_0000, // 1GB per channel
    parameter int T_RCD = 15,             // RAS to CAS delay (ns)
    parameter int T_RP = 15,              // Row precharge time (ns)
    parameter int T_RAS = 35,             // Row active time (ns)
    parameter int T_RC = 50,              // Row cycle time (ns)
    parameter int T_CL = 14,              // CAS latency (cycles)
    parameter int T_BL = 4                // Burst length (cycles)
) (
    // Clock and Reset
    input  logic                    clk,
    input  logic                    rst_n,
    
    // Command Interface
    input  logic                    cmd_valid,
    input  hbm_cmd_t               cmd,
    output logic                    cmd_ready,
    
    // Write Data Interface
    input  logic                    wdata_valid,
    input  logic [DATA_WIDTH-1:0]   wdata,
    input  logic [DATA_WIDTH/8-1:0] wdata_strb,
    output logic                    wdata_ready,
    
    // Read Data Interface
    output logic                    rdata_valid,
    output logic [DATA_WIDTH-1:0]   rdata,
    output logic [7:0]              rdata_id,
    input  logic                    rdata_ready,
    
    // Status
    output logic [NUM_CHANNELS-1:0] channel_busy,
    output logic [31:0]            read_count,
    output logic [31:0]            write_count,
    output logic [31:0]            total_latency
);

    // =========================================================================
    // Internal Types and Structures
    // =========================================================================
    
    typedef struct packed {
        logic valid;
        logic [ADDR_WIDTH-1:0] addr;
        logic [7:0] id;
        logic [3:0] len;
        logic [2:0] size;
        hbm_cmd_type_t cmd_type;
        logic [31:0] timestamp;
    } pending_cmd_t;
    
    typedef struct packed {
        logic [ADDR_WIDTH-1:0] row_addr;
        logic active;
        logic [31:0] activate_time;
    } bank_state_t;
    
    typedef struct packed {
        bank_state_t banks [NUM_BANKS-1:0];
        logic [31:0] last_cmd_time;
        logic busy;
    } channel_state_t;

    // =========================================================================
    // Internal Memory Storage
    // =========================================================================
    
    // 简化的内存存储 - 每个通道1GB
    logic [7:0] memory [NUM_CHANNELS-1:0][MEMORY_SIZE-1:0];
    
    // Channel states
    channel_state_t channel_states [NUM_CHANNELS-1:0];
    
    // Command and data queues
    pending_cmd_t cmd_queue [$];
    logic [DATA_WIDTH-1:0] write_data_queue [$];
    logic [DATA_WIDTH/8-1:0] write_strb_queue [$];
    
    // Read response queue
    typedef struct packed {
        logic [DATA_WIDTH-1:0] data;
        logic [7:0] id;
        logic [31:0] ready_time;
    } read_resp_t;
    
    read_resp_t read_resp_queue [$];
    
    // Statistics
    logic [31:0] read_count_reg;
    logic [31:0] write_count_reg;
    logic [31:0] total_latency_reg;
    logic [31:0] current_time;
    
    // Control signals
    logic [31:0] cmd_process_delay;
    logic [31:0] read_latency;

    // =========================================================================
    // Memory Initialization
    // =========================================================================
    
    initial begin
        // Initialize memory with pattern for debugging
        for (int ch = 0; ch < NUM_CHANNELS; ch++) begin
            for (int addr = 0; addr < 1024; addr++) begin
                memory[ch][addr] = addr[7:0] ^ ch[7:0];
            end
        end
    end

    // =========================================================================
    // Command Interface Logic
    // =========================================================================
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            cmd_ready <= 1'b1;
            current_time <= '0;
            read_count_reg <= '0;
            write_count_reg <= '0;
            total_latency_reg <= '0;
            channel_states <= '{default: '0};
        end else begin
            current_time <= current_time + 1;
            
            // Accept new commands
            if (cmd_valid && cmd_ready) begin
                pending_cmd_t new_cmd;
                new_cmd.valid = 1'b1;
                new_cmd.addr = cmd.addr;
                new_cmd.id = cmd.id;
                new_cmd.len = cmd.len;
                new_cmd.size = cmd.size;
                new_cmd.cmd_type = cmd.cmd_type;
                new_cmd.timestamp = current_time;
                
                cmd_queue.push_back(new_cmd);
                
                // Simple backpressure - don't accept if queue is too full
                if (cmd_queue.size() > 16) begin
                    cmd_ready <= 1'b0;
                end
            end else if (cmd_queue.size() < 8) begin
                cmd_ready <= 1'b1;
            end
        end
    end

    // =========================================================================
    // Write Data Interface
    // =========================================================================
    
    assign wdata_ready = (write_data_queue.size() < 32);
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // Reset handled above
        end else if (wdata_valid && wdata_ready) begin
            write_data_queue.push_back(wdata);
            write_strb_queue.push_back(wdata_strb);
        end
    end

    // =========================================================================
    // Command Processing Logic
    // =========================================================================
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            cmd_process_delay <= '0;
            read_latency <= T_CL + T_BL;
        end else begin
            // Process pending commands
            if (cmd_queue.size() > 0 && cmd_process_delay == 0) begin
                pending_cmd_t current_cmd = cmd_queue[0];
                cmd_queue.pop_front();
                
                logic [3:0] channel_id = current_cmd.addr[3:0] % NUM_CHANNELS;
                logic [3:0] bank_id = current_cmd.addr[7:4] % NUM_BANKS;
                logic [ADDR_WIDTH-1:0] row_addr = current_cmd.addr[ADDR_WIDTH-1:12];
                logic [ADDR_WIDTH-1:0] byte_addr = current_cmd.addr;
                
                // Simulate bank conflicts and timing
                logic bank_conflict = channel_states[channel_id].banks[bank_id].active &&
                                    (channel_states[channel_id].banks[bank_id].row_addr != row_addr);
                
                logic [31:0] timing_delay = bank_conflict ? (T_RP + T_RCD) : T_RCD;
                
                if (current_cmd.cmd_type == HBM_CMD_READ) begin
                    // Schedule read response
                    read_resp_t read_resp;
                    
                    // Read data from memory
                    for (int i = 0; i < DATA_WIDTH/8; i++) begin
                        if (byte_addr + i < MEMORY_SIZE) begin
                            read_resp.data[i*8 +: 8] = memory[channel_id][byte_addr + i];
                        end else begin
                            read_resp.data[i*8 +: 8] = 8'hXX;
                        end
                    end
                    
                    read_resp.id = current_cmd.id;
                    read_resp.ready_time = current_time + timing_delay + read_latency;
                    read_resp_queue.push_back(read_resp);
                    
                    read_count_reg <= read_count_reg + 1;
                    total_latency_reg <= total_latency_reg + timing_delay + read_latency;
                    
                end else if (current_cmd.cmd_type == HBM_CMD_WRITE) begin
                    // Process write command
                    if (write_data_queue.size() > 0) begin
                        logic [DATA_WIDTH-1:0] write_data = write_data_queue[0];
                        logic [DATA_WIDTH/8-1:0] write_strb = write_strb_queue[0];
                        write_data_queue.pop_front();
                        write_strb_queue.pop_front();
                        
                        // Write data to memory
                        for (int i = 0; i < DATA_WIDTH/8; i++) begin
                            if (write_strb[i] && (byte_addr + i < MEMORY_SIZE)) begin
                                memory[channel_id][byte_addr + i] = write_data[i*8 +: 8];
                            end
                        end
                        
                        write_count_reg <= write_count_reg + 1;
                    end
                end
                
                // Update bank state
                channel_states[channel_id].banks[bank_id].active <= 1'b1;
                channel_states[channel_id].banks[bank_id].row_addr <= row_addr;
                channel_states[channel_id].banks[bank_id].activate_time <= current_time;
                channel_states[channel_id].last_cmd_time <= current_time;
                
                cmd_process_delay <= timing_delay;
            end else if (cmd_process_delay > 0) begin
                cmd_process_delay <= cmd_process_delay - 1;
            end
        end
    end

    // =========================================================================
    // Read Response Logic
    // =========================================================================
    
    logic rdata_valid_int;
    logic [DATA_WIDTH-1:0] rdata_int;
    logic [7:0] rdata_id_int;
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            rdata_valid_int <= 1'b0;
            rdata_int <= '0;
            rdata_id_int <= '0;
        end else begin
            rdata_valid_int <= 1'b0;
            
            if (read_resp_queue.size() > 0) begin
                read_resp_t resp = read_resp_queue[0];
                if (current_time >= resp.ready_time) begin
                    rdata_valid_int <= 1'b1;
                    rdata_int <= resp.data;
                    rdata_id_int <= resp.id;
                    
                    if (rdata_ready) begin
                        read_resp_queue.pop_front();
                    end
                end
            end
        end
    end
    
    assign rdata_valid = rdata_valid_int;
    assign rdata = rdata_int;
    assign rdata_id = rdata_id_int;

    // =========================================================================
    // Status and Channel Busy Logic
    // =========================================================================
    
    always_comb begin
        for (int ch = 0; ch < NUM_CHANNELS; ch++) begin
            channel_busy[ch] = (current_time - channel_states[ch].last_cmd_time) < 10;
        end
    end
    
    assign read_count = read_count_reg;
    assign write_count = write_count_reg;
    assign total_latency = total_latency_reg;

    // =========================================================================
    // Debug and Monitoring
    // =========================================================================
    
    `ifdef SIMULATION
    // Performance monitoring
    always @(posedge clk) begin
        if (cmd_valid && cmd_ready) begin
            $display("[HBM] Time=%0t, Cmd=%s, Addr=0x%h, ID=%0d", 
                    $time, cmd.cmd_type.name(), cmd.addr, cmd.id);
        end
        
        if (rdata_valid && rdata_ready) begin
            $display("[HBM] Time=%0t, Read Response, ID=%0d, Data=0x%h", 
                    $time, rdata_id, rdata[63:0]);
        end
    end
    
    // Warning for bank conflicts
    always @(posedge clk) begin
        if (cmd_valid && cmd_ready && cmd.cmd_type != HBM_CMD_NOP) begin
            logic [3:0] channel_id = cmd.addr[3:0] % NUM_CHANNELS;
            logic [3:0] bank_id = cmd.addr[7:4] % NUM_BANKS;
            logic [ADDR_WIDTH-1:0] row_addr = cmd.addr[ADDR_WIDTH-1:12];
            
            if (channel_states[channel_id].banks[bank_id].active &&
                channel_states[channel_id].banks[bank_id].row_addr != row_addr) begin
                $display("[HBM] WARNING: Bank conflict detected at time %0t, Channel=%0d, Bank=%0d", 
                        $time, channel_id, bank_id);
            end
        end
    end
    `endif

endmodule