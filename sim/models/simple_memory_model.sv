// simple_memory_model.sv
// Simple Memory Model for Verilator simulation
// Compatible with memory_interface.sv

import nmcu_pkg::*;
import nmcu_types::*;

module simple_memory_model #(
    parameter int MEMORY_SIZE = 32'h1000_0000,  // 256MB
    parameter int LATENCY_CYCLES = 10
) (
    mem_interface.slave mem_if
);

    // Memory storage
    logic [7:0] memory [0:MEMORY_SIZE-1];
    
    // Internal state
    typedef enum logic [2:0] {
        IDLE,
        PROCESSING,
        READ_RESPONSE,
        WRITE_RESPONSE,
        ERROR_STATE
    } mem_state_t;
    
    mem_state_t state, next_state;
    
    // Request storage
    logic [ADDR_WIDTH-1:0]      req_addr_reg;
    logic [MEM_DATA_WIDTH-1:0]  req_data_reg;
    logic [2:0]                 req_cmd_reg;
    logic [7:0]                 req_burst_len_reg;
    logic [3:0]                 req_id_reg;
    logic [MEM_DATA_WIDTH/8-1:0] req_be_reg;
    
    // Response data
    logic [MEM_DATA_WIDTH-1:0]  resp_data_reg;
    logic [3:0]                 resp_id_reg;
    logic                       resp_error_reg;
    
    // Timing
    logic [7:0]                 latency_counter;
    logic [31:0]                cycle_counter;
    
    // Statistics
    logic [31:0]                read_count;
    logic [31:0]                write_count;
    
    // Memory initialization
    initial begin
        // Initialize memory with a pattern for debugging
        for (int i = 0; i < 1024; i++) begin
            memory[i] = i[7:0];
        end
        
        // Initialize remaining memory to zero
        for (int i = 1024; i < MEMORY_SIZE; i++) begin
            memory[i] = 8'h00;
        end
        
        $display("[Memory Model] Initialized %0d bytes of memory", MEMORY_SIZE);
    end
    
    // State machine
    always_ff @(posedge mem_if.clk or negedge mem_if.rst_n) begin
        if (!mem_if.rst_n) begin
            state <= IDLE;
            latency_counter <= '0;
            cycle_counter <= '0;
            read_count <= '0;
            write_count <= '0;
            req_addr_reg <= '0;
            req_data_reg <= '0;
            req_cmd_reg <= '0;
            req_burst_len_reg <= '0;
            req_id_reg <= '0;
            req_be_reg <= '0;
            resp_data_reg <= '0;
            resp_id_reg <= '0;
            resp_error_reg <= '0;
        end else begin
            state <= next_state;
            cycle_counter <= cycle_counter + 1;
            
            case (state)
                IDLE: begin
                    if (mem_if.req_valid) begin
                        // Capture request
                        req_addr_reg <= mem_if.req_addr;
                        req_data_reg <= mem_if.req_data;
                        req_cmd_reg <= mem_if.req_cmd;
                        req_burst_len_reg <= mem_if.req_burst_len;
                        req_id_reg <= mem_if.req_id;
                        req_be_reg <= mem_if.req_be;
                        latency_counter <= LATENCY_CYCLES[7:0];
                    end
                end
                
                PROCESSING: begin
                    if (latency_counter > 0) begin
                        latency_counter <= latency_counter - 1;
                    end else begin
                        // Process the request
                        case (req_cmd_reg)
                            3'b000: begin // MEM_READ
                                read_memory();
                                read_count <= read_count + 1;
                            end
                            3'b001: begin // MEM_WRITE
                                write_memory();
                                write_count <= write_count + 1;
                            end
                            default: begin
                                resp_error_reg <= 1'b1;
                            end
                        endcase
                        resp_id_reg <= req_id_reg;
                    end
                end
                
                READ_RESPONSE, WRITE_RESPONSE: begin
                    // Wait for ready
                    if (mem_if.resp_ready) begin
                        resp_error_reg <= 1'b0;
                    end
                end
                
                ERROR_STATE: begin
                    if (mem_if.resp_ready) begin
                        resp_error_reg <= 1'b0;
                    end
                end
            endcase
        end
    end
    
    // Next state logic
    always_comb begin
        next_state = state;
        
        case (state)
            IDLE: begin
                if (mem_if.req_valid && mem_if.req_ready) begin
                    next_state = PROCESSING;
                end
            end
            
            PROCESSING: begin
                if (latency_counter == 0) begin
                    case (req_cmd_reg)
                        3'b000: next_state = READ_RESPONSE;  // MEM_READ
                        3'b001: next_state = WRITE_RESPONSE; // MEM_WRITE
                        default: next_state = ERROR_STATE;
                    endcase
                end
            end
            
            READ_RESPONSE, WRITE_RESPONSE: begin
                if (mem_if.resp_ready) begin
                    next_state = IDLE;
                end
            end
            
            ERROR_STATE: begin
                if (mem_if.resp_ready) begin
                    next_state = IDLE;
                end
            end
        endcase
    end
    
    // Memory read function
    function void read_memory();
        logic [ADDR_WIDTH-1:0] addr;
        addr = req_addr_reg;
        
        // Check address bounds
        if (addr >= MEMORY_SIZE) begin
            resp_data_reg <= {MEM_DATA_WIDTH{1'bx}};
            resp_error_reg <= 1'b1;
            return;
        end
        
        // Read data (little endian)
        for (int i = 0; i < MEM_DATA_WIDTH/8; i++) begin
            if (addr + i < MEMORY_SIZE) begin
                resp_data_reg[i*8 +: 8] <= memory[addr + i];
            end else begin
                resp_data_reg[i*8 +: 8] <= 8'h00;
            end
        end
        
        resp_error_reg <= 1'b0;
    endfunction
    
    // Memory write function
    function void write_memory();
        logic [ADDR_WIDTH-1:0] addr;
        addr = req_addr_reg;
        
        // Check address bounds
        if (addr >= MEMORY_SIZE) begin
            resp_error_reg <= 1'b1;
            return;
        end
        
        // Write data with byte enables
        for (int i = 0; i < MEM_DATA_WIDTH/8; i++) begin
            if (req_be_reg[i] && (addr + i < MEMORY_SIZE)) begin
                memory[addr + i] <= req_data_reg[i*8 +: 8];
            end
        end
        
        resp_error_reg <= 1'b0;
    endfunction
    
    // Interface assignments
    assign mem_if.req_ready = (state == IDLE);
    assign mem_if.resp_valid = (state == READ_RESPONSE) || 
                               (state == WRITE_RESPONSE) || 
                               (state == ERROR_STATE);
    assign mem_if.resp_data = resp_data_reg;
    assign mem_if.resp_id = resp_id_reg;
    assign mem_if.resp_error = resp_error_reg;
    assign mem_if.resp_status = resp_error_reg ? 2'b11 : 2'b00;
    assign mem_if.resp_tag = resp_id_reg;
    
    // Memory status
    assign mem_if.mem_init_done = 1'b1;
    assign mem_if.mem_cal_done = 1'b1;
    assign mem_if.mem_status = 8'h01; // Ready status
    
    // Debug output
    `ifdef SIMULATION
        always @(posedge mem_if.clk) begin
            if (mem_if.req_valid && mem_if.req_ready) begin
                $display("[Memory Model] Request: cmd=%0d, addr=0x%h, id=%0d", 
                         mem_if.req_cmd, mem_if.req_addr, mem_if.req_id);
            end
            
            if (mem_if.resp_valid && mem_if.resp_ready) begin
                $display("[Memory Model] Response: data=0x%h, id=%0d, error=%0b", 
                         mem_if.resp_data[63:0], mem_if.resp_id, mem_if.resp_error);
            end
        end
    `endif

endmodule
