// ucie_ip_model.sv
// Simple UCIe IP Model for Verilator simulation
// Provides basic loopback functionality for testing

`include "parameters.sv"
`include "types.sv"

module ucie_ip_model #(
    parameter int LANES = 8,
    parameter int DATA_WIDTH = 256
) (
    input  logic                    tx_clk,
    input  logic                    rx_clk,
    input  logic                    rst_n,

    // TX Interface (receives from DUT)
    input  logic [DATA_WIDTH-1:0]   tx_data,
    input  logic                    tx_valid,
    output logic                    tx_ready,

    // RX Interface (sends to DUT)
    output logic [DATA_WIDTH-1:0]   rx_data,
    output logic                    rx_valid,
    input  logic                    rx_ready
);

    import nmcu_pkg::*;
    import nmcu_types::*;

    // Internal FIFO for loopback
    localparam int FifoDepth = 16;
    localparam int FifoAddrWidth = $clog2(FifoDepth);

    logic [DATA_WIDTH-1:0]          fifo_mem [FifoDepth];
    logic [FifoAddrWidth:0]         wr_ptr, rd_ptr;
    logic                           fifo_full, fifo_empty;
    logic                           fifo_wr_en, fifo_rd_en;

    // Link status
    logic                           link_up;
    logic [7:0]                     link_training_count;

    // FIFO control
    assign fifo_full = (wr_ptr[FifoAddrWidth] != rd_ptr[FifoAddrWidth]) &&
                       (wr_ptr[FifoAddrWidth-1:0] == rd_ptr[FifoAddrWidth-1:0]);
    assign fifo_empty = (wr_ptr == rd_ptr);

    assign fifo_wr_en = tx_valid && tx_ready && link_up;
    assign fifo_rd_en = rx_valid && rx_ready && !fifo_empty;

    assign tx_ready = !fifo_full && link_up;
    assign rx_valid = !fifo_empty && link_up;

    // FIFO write
    always_ff @(posedge tx_clk or negedge rst_n) begin
        if (!rst_n) begin
            wr_ptr <= '0;
        end else if (fifo_wr_en) begin
            fifo_mem[wr_ptr[FifoAddrWidth-1:0]] <= tx_data;
            wr_ptr <= wr_ptr + 1;
        end
    end

    // FIFO read
    always_ff @(posedge rx_clk or negedge rst_n) begin
        if (!rst_n) begin
            rd_ptr <= '0;
            rx_data <= '0;
        end else if (fifo_rd_en) begin
            rd_ptr <= rd_ptr + 1;
        end

        // Output data (combinational read)
        if (!fifo_empty) begin
            rx_data <= fifo_mem[rd_ptr[FifoAddrWidth-1:0]];
        end else begin
            rx_data <= '0;
        end
    end

    // Link training simulation
    always_ff @(posedge tx_clk or negedge rst_n) begin
        if (!rst_n) begin
            link_up <= 1'b0;
            link_training_count <= '0;
        end else begin
            if (link_training_count < 8'd100) begin
                link_training_count <= link_training_count + 1;
                link_up <= 1'b0;
            end else begin
                link_up <= 1'b1;
            end
        end
    end

    // Simple packet processing (for demonstration)
    logic [31:0] packet_count_tx, packet_count_rx;

    always_ff @(posedge tx_clk or negedge rst_n) begin
        if (!rst_n) begin
            packet_count_tx <= '0;
        end else if (fifo_wr_en) begin
            packet_count_tx <= packet_count_tx + 1;
        end
    end

    always_ff @(posedge rx_clk or negedge rst_n) begin
        if (!rst_n) begin
            packet_count_rx <= '0;
        end else if (fifo_rd_en) begin
            packet_count_rx <= packet_count_rx + 1;
        end
    end

    // Debug and monitoring
    `ifdef SIMULATION
        always @(posedge tx_clk) begin
            if (fifo_wr_en) begin
                $display("[UCIe Model] TX: data=0x%h, count=%0d", tx_data, packet_count_tx);
            end
        end

        always @(posedge rx_clk) begin
            if (fifo_rd_en) begin
                $display("[UCIe Model] RX: data=0x%h, count=%0d", rx_data, packet_count_rx);
            end
        end

        initial begin
            $display("[UCIe Model] Initialized with %0d lanes, %0d-bit data width",
                     LANES, DATA_WIDTH);
        end
    `endif

endmodule