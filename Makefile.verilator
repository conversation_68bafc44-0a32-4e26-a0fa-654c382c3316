# Makefile.verilator
#
# This Makefile compiles the nmcu_chiplet design and testbench using Verilator.
# It generates a C++ executable that simulates the SystemVerilog code.
# This version is designed to run purely from the SystemVerilog testbench,
# using the comprehensive nmcu_chiplet_tb as the top module.

# --- Configuration ---
VERILATOR_EXE   = verilator
TOP_MODULE      = nmcu_chiplet_tb # Changed to the comprehensive top-level testbench
SIM_BUILD_DIR   = ./obj_dir
SIM_EXE_NAME    = $(TOP_MODULE)_sim
TRACE_FILE      = $(TOP_MODULE).fst # Output FST file
RM              = rm -rf

# --- Source Files ---
# All core design files
SRCS := \
	src/common/parameters.sv \
	src/common/types.sv \
	src/top/nmcu_chiplet.sv \
	src/cache/cache_controller.sv \
	src/cache/cache_memory.sv \
	src/cache/mshr.sv \
	src/cache/prefetcher.sv \
	src/control/control_unit.sv \
	src/interconnect/ucie_adapter.sv \
	src/memory_if/mem_interface_controller.sv \
	src/pe_array/pe_array.sv \
	src/pe_array/pe_interface.sv \
	src/pe_array/pe.sv

# All testbench files and behavioral models
SRCS += \
	sim/models/ddr_model.sv \
	sim/models/hbm_model.sv \
	sim/models/simple_memory_model.sv \
	sim/models/ucie_ip_model.sv \
	sim/tb/common/tb_parameters.sv \
	sim/tb/common/tb_types.sv \
	sim/tb/interfaces/cache_interface.sv \
	sim/tb/interfaces/control_interface.sv \
	sim/tb/interfaces/mem_interface.sv \
	sim/tb/interfaces/memory_interface.sv \
	sim/tb/interfaces/nmcu_interconnect_if.sv \
	sim/tb/interfaces/nmcu_memory_if.sv \
	sim/tb/interfaces/pe_interface.sv \
	sim/tb/interfaces/ucie_interface.sv \
	sim/tb/nmcu_tb_pkg.sv \
	sim/tb/top/nmcu_chiplet_tb.sv # New top-level testbench file

# --- Include Directories ---
# Verilator uses -y for directories to search for modules/packages/includes.
# Be comprehensive to ensure all dependencies are found.
INC_DIRS := \
	src \
	src/cache \
	src/common \
	src/control \
	src/interconnect \
	src/memory_if \
	src/pe_array \
	src/top \
	sim/models \
	sim/tb \
	sim/tb/common \
	sim/tb/interfaces \
	sim/tb/top

VERILATOR_INCLUDE_OPTS = $(foreach dir,$(INC_DIRS),-y $(dir))

# --- Verilator Flags ---
# --sv: Enable SystemVerilog features
# --cc: Generate C++ code for the simulator
# --exe: Link the generated C++ code into an executable
#   (When --exe is used without a custom main.cpp, Verilator generates a default C++ main
#    that runs the simulation based on Verilog initial/always blocks and responds to $finish.)
# --build: Automatically build the generated C++ code (runs make internally)
# --trace: Enable waveform tracing
# --trace-fst: Use FST format (more efficient than VCD)
# --timescale 1ns/1ps: Set default timescale for simulation
# -top <module>: Specify the top-level module for simulation
# -Mdir <dir>: Specify the directory for generated files
# -o <name>: Specify the name of the executable
# Removed --no-timing as it can interfere with time advancement based on # delays.
VERILATOR_FLAGS = \
	--sv \
	--cc \
	--exe \
	--build \
	--trace \
	--trace-fst \
	--timescale 1ns/1ps \
	-top $(TOP_MODULE) \
	$(VERILATOR_INCLUDE_OPTS) \
	-Mdir $(SIM_BUILD_DIR) \
	-o $(SIM_EXE_NAME)

# --- Makefile Targets ---

.PHONY: all build run clean help

all: build run

build: $(SIM_BUILD_DIR)/$(SIM_EXE_NAME)

$(SIM_BUILD_DIR)/$(SIM_EXE_NAME): $(SRCS)
	@echo "----------------------------------------------------"
	@echo " Compiling with Verilator (Pure SV Testbench)..."
	@echo " Top Module: $(TOP_MODULE)"
	@echo " Output Dir: $(SIM_BUILD_DIR)"
	@echo " Executable: $(SIM_EXE_NAME)"
	@echo "----------------------------------------------------"
	$(VERILATOR_EXE) $(VERILATOR_FLAGS) $(SRCS)
	@echo "Verilator compilation complete. Executable: $(SIM_BUILD_DIR)/$(SIM_EXE_NAME)"

run: build
	@echo "----------------------------------------------------"
	@echo " Running Verilator simulation (Pure SV Testbench)..."
	@echo " Waveform will be saved to: $(TRACE_FILE)"
	@echo "----------------------------------------------------"
	# The executable generated by Verilator with --exe automatically enables tracing
	# if --trace was given during compilation. It will use the -o name + .fst
	$(SIM_BUILD_DIR)/$(SIM_EXE_NAME)
	@echo "Simulation complete. Waveform saved to $(TRACE_FILE)"
	@echo "To view waveforms, run: gtkwave $(TRACE_FILE)"

clean:
	@echo "----------------------------------------------------"
	@echo " Cleaning Verilator build artifacts..."
	@echo "----------------------------------------------------"
	$(RM) $(SIM_BUILD_DIR)
	$(RM) $(TRACE_FILE)
	$(RM) $(TOP_MODULE).vcd # In case VCD was used before
	@echo "Clean complete."

help:
	@echo "Usage: make -f Makefile.verilator [target]"
	@echo ""
	@echo "Targets:"
	@echo "  all    : Builds and runs the simulation (default)."
	@echo "  build  : Compiles the SystemVerilog design and testbench."
	@echo "  run    : Executes the compiled simulation."
	@echo "  clean  : Removes all generated build files and waveforms."
	@echo "  help   : Displays this help message."
