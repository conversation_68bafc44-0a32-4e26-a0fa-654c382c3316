# Makefile.vcs
#
# This Makefile compiles the nmcu_chiplet design and testbench using Synopsys VCS.

# --- Configuration ---
VCS_EXE         = vcs
TOP_MODULE      = nmcu_chiplet_tb
SIM_EXE_NAME    = simv
TRACE_FILE      = $(TOP_MODULE).fsdb # Using FSDB for VCS
RM              = rm -rf

# --- Source Files ---
# Find all SystemVerilog files in src/ and sim/
SRCS := $(shell find src -name "*.sv")
SRCS += $(shell find sim/models -name "*.sv")
SRCS += $(shell find sim/tb -name "*.sv")

# --- Include Directories ---
# VCS uses +incdir+ for directories to search for packages and 'include files.
# Be comprehensive to ensure all dependencies are found.
INC_DIRS := \
	src \
	src/cache \
	src/common \
	src/control \
	src/interconnect \
	src/memory_if \
	src/pe_array \
	src/top \
	sim/models \
	sim/tb \
	sim/tb/common \
	sim/tb/interfaces \
	sim/tb/top

VCS_INCLUDE_OPTS = $(foreach dir,$(INC_DIRS),+incdir+$(dir))

# --- VCS Flags ---
# -sverilog: Enable SystemVerilog features
# -full64: Compile for 64-bit architecture
# -timescale=1ns/1ps: Set default timescale
# +lint=all: Enable all linting checks (optional, but good practice)
# -debug_access+all: Enable full debug capabilities (required for waveform dumping)
# -l compile.log: Log compilation output to a file
# -top <module>: Specify the top-level module for simulation
# -o <name>: Specify the name of the executable
VCS_FLAGS = \
	-sverilog \
	-full64 \
	-timescale=1ns/1ps \
	+lint=all \
	-debug_access+all \
	-l compile.log \
	-top $(TOP_MODULE) \
	$(VCS_INCLUDE_OPTS) \
	-o $(SIM_EXE_NAME)

# --- Makefile Targets ---

.PHONY: all build run clean help

all: build run

build: $(SIM_EXE_NAME)

$(SIM_EXE_NAME): $(SRCS)
	@echo "----------------------------------------------------"
	@echo " Compiling with VCS..."
	@echo " Top Module: $(TOP_MODULE)"
	@echo " Executable: $(SIM_EXE_NAME)"
	@echo "----------------------------------------------------"
	$(VCS_EXE) $(VCS_FLAGS) $(SRCS)
	@echo "VCS compilation complete. Executable: $(SIM_EXE_NAME)"
	@echo "Compilation log: compile.log"

run: build
	@echo "----------------------------------------------------"
	@echo " Running VCS simulation..."
	@echo " Waveform will be saved to: $(TRACE_FILE)"
	@echo "----------------------------------------------------"
	./$(SIM_EXE_NAME) +fsdbfile=$(TRACE_FILE)
	@echo "Simulation complete. Waveform saved to $(TRACE_FILE)"
	@echo "To view waveforms, run: verdi -sv $(TRACE_FILE)"

clean:
	@echo "----------------------------------------------------"
	@echo " Cleaning VCS build artifacts..."
	@echo "----------------------------------------------------"
	$(RM) csrc/
	$(RM) $(SIM_EXE_NAME)
	$(RM) $(SIM_EXE_NAME).daidir/
	$(RM) *.log
	$(RM) ucli.key
	$(RM) vc_hdrs.h
	$(RM) $(TRACE_FILE)
	@echo "Clean complete."

help:
	@echo "Usage: make -f Makefile.vcs [target]"
	@echo ""
	@echo "Targets:"
	@echo "  all    : Builds and runs the simulation (default)."
	@echo "  build  : Compiles the SystemVerilog design and testbench."
	@echo "  run    : Executes the compiled simulation."
	@echo "  clean  : Removes all generated build files and waveforms."
	@echo "  help   : Displays this help message."
