# NMCU_Chiplet

## General architecture
```
+-------------------------------------------------------------------+
|                           NMCU Chiplet                            |
|                                                                   |
|  +---------------------------+      +--------------------------+  |
|  | Chiplet Interconnect I/F  |<---->|     Control Unit &       |  |
|  | (e.g., UCIe Adapter)      |      |     Instruction Decoder  |  |
|  +---------------------------+      +------------+-------------+  |
|                                                  |                |
|                                      (Commands,   |                |
|                                       Addresses) |                |
|                                                  v                |
|  +---------------------------------------------------------------+  |
|  |                         Cache System                          |  |
|  | +----------------------+  +---------------------------------+ |  |
|  | | Cache Controller     |  |          Cache Memory           | |  |
|  | | (incl. MSHR,         |<->| +-----------+   +-----------+   | |  |
|  | |  Prefetcher)         |  | | Tag Array |   | Data Array|   | |  |
|  | +----------------------+  | +-----------+   +-----------+   | |  |
|  |           ^              +---------------------------------+ |  |
|  +-----------|--------------------------------------------------+  |
|              | (Hit/Miss, Data)                                   |
|   +----------+------------------+     +------------------------+   |
|   | PE Array Interface/          |     |  Memory Interface      |   |
|   | Data Dispatcher (FIFOs)      |<--->|  Controller (to HBM/   |   |
|   +----------+------------------+     |  DDR/SRAM)             |   |
|              |                       +------------------------+   |
|              v                                                    |
|  +---------------------------+                                    |
|  |  Processing Element Array |                                    |
|  |  (e.g., Systolic Array)   |                                    |
|  |  +----+----+----+----+    |                                    |
|  |  | PE | PE | PE | PE |    |                                    |
|  |  +----+----+----+----+    |                                    |
|  |  | PE | PE | PE | PE |    |                                    |
|  |  +----+----+----+----+    |                                    |
|  +---------------------------+                                    |
|                                                                   |
+-------------------------------------------------------------------+
```
